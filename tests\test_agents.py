"""
智能体系统测试
"""
import pytest
import asyncio
from agents.base_agent import Message, AgentStatus
from agents.worker_agent import WorkerAgent
from coordinator import TaskCoordinator

class TestWorkerAgent:
    """工作智能体测试"""
    
    @pytest.mark.asyncio
    async def test_agent_creation(self):
        """测试智能体创建"""
        agent = WorkerAgent("test_1", "测试智能体", "testing")
        assert agent.agent_id == "test_1"
        assert agent.name == "测试智能体"
        assert agent.specialization == "testing"
        assert agent.status == AgentStatus.IDLE
        
    @pytest.mark.asyncio
    async def test_agent_start_stop(self):
        """测试智能体启动和停止"""
        agent = WorkerAgent("test_2", "测试智能体2", "testing")
        
        await agent.start()
        assert agent.running == True
        assert agent.status == AgentStatus.IDLE
        
        await agent.stop()
        assert agent.running == False
        assert agent.status == AgentStatus.OFFLINE
        
    @pytest.mark.asyncio
    async def test_task_execution(self):
        """测试任务执行"""
        agent = WorkerAgent("test_3", "测试智能体3", "testing")
        await agent.start()
        
        task = {
            "id": "task_1",
            "type": "test_task",
            "description": "测试任务",
            "required_capability": "testing"
        }
        
        result = await agent.execute_task(task)
        
        assert "success" in result
        assert "execution_time" in result
        assert result["agent_id"] == "test_3"
        assert result["specialization"] == "testing"
        
        await agent.stop()

class TestTaskCoordinator:
    """任务协调器测试"""
    
    @pytest.mark.asyncio
    async def test_coordinator_creation(self):
        """测试协调器创建"""
        coordinator = TaskCoordinator()
        assert len(coordinator.agents) == 0
        assert len(coordinator.tasks) == 0
        assert coordinator.running == False
        
    @pytest.mark.asyncio
    async def test_task_creation(self):
        """测试任务创建"""
        coordinator = TaskCoordinator()
        
        task_id = await coordinator.create_task(
            "test_task", 
            "测试任务描述", 
            "basic_tasks"
        )
        
        assert task_id in coordinator.tasks
        task = coordinator.tasks[task_id]
        assert task["type"] == "test_task"
        assert task["description"] == "测试任务描述"
        assert task["status"] == "pending"
        
    @pytest.mark.asyncio
    async def test_system_integration(self):
        """测试系统集成"""
        coordinator = TaskCoordinator()
        await coordinator.start()

        # 等待智能体启动
        await asyncio.sleep(0.5)

        # 创建任务
        task_id = await coordinator.create_task(
            "integration_test",
            "集成测试任务",
            "basic_tasks"
        )

        # 等待任务分配和执行
        await asyncio.sleep(3)

        # 检查任务状态
        task = coordinator.tasks[task_id]
        assert task["status"] in ["assigned", "completed"]

        # 检查系统状态
        status = coordinator.get_system_status()
        assert len(status["agents"]) > 0
        assert status["tasks"]["total"] > 0

        await coordinator.stop()

    @pytest.mark.asyncio
    async def test_task_cleanup(self):
        """测试任务清理功能"""
        coordinator = TaskCoordinator()
        await coordinator.start()

        # 验证后台任务已创建
        assert coordinator._task_assignment_task is not None
        assert not coordinator._task_assignment_task.done()

        # 验证智能体的消息循环任务已创建
        for agent in coordinator.agents.values():
            assert agent._message_loop_task is not None
            assert not agent._message_loop_task.done()

        # 停止协调器
        await coordinator.stop()

        # 验证所有任务都已被取消
        assert coordinator._task_assignment_task.cancelled() or coordinator._task_assignment_task.done()

        for agent in coordinator.agents.values():
            assert agent._message_loop_task.cancelled() or agent._message_loop_task.done()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
