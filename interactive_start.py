"""
交互式启动A2A多智能体系统
"""
import asyncio
from coordinator import TaskCoordinator

async def interactive_demo():
    """交互式演示"""
    print("🎯 A2A多智能体协作系统")
    print("=" * 40)
    
    coordinator = TaskCoordinator()
    await coordinator.start()
    
    try:
        while True:
            print("\n📋 可用操作:")
            print("1. 创建数据分析任务")
            print("2. 创建文本处理任务") 
            print("3. 创建计算任务")
            print("4. 查看系统状态")
            print("5. 退出系统")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == "1":
                desc = input("请输入数据分析任务描述: ").strip()
                if desc:
                    task_id = await coordinator.create_task("数据分析", desc, "data_analysis")
                    print(f"✅ 已创建任务: {task_id}")
                    
            elif choice == "2":
                desc = input("请输入文本处理任务描述: ").strip()
                if desc:
                    task_id = await coordinator.create_task("文本处理", desc, "text_processing")
                    print(f"✅ 已创建任务: {task_id}")
                    
            elif choice == "3":
                desc = input("请输入计算任务描述: ").strip()
                if desc:
                    task_id = await coordinator.create_task("计算任务", desc, "computation")
                    print(f"✅ 已创建任务: {task_id}")
                    
            elif choice == "4":
                status = coordinator.get_system_status()
                print(f"\n📊 系统状态:")
                print(f"🤖 智能体状态:")
                for agent_id, agent_info in status['agents'].items():
                    print(f"  • {agent_info['name']}: {agent_info['status']}")
                    
                print(f"\n📈 任务统计:")
                tasks_info = status['tasks']
                print(f"  • 总任务数: {tasks_info['total']}")
                print(f"  • 待处理: {tasks_info['pending']}")
                print(f"  • 执行中: {tasks_info['assigned']}")
                print(f"  • 已完成: {tasks_info['completed']}")
                
                if tasks_info['total'] > 0:
                    completion_rate = (tasks_info['completed'] / tasks_info['total']) * 100
                    print(f"  • 完成率: {completion_rate:.1f}%")
                    
            elif choice == "5":
                print("👋 正在关闭系统...")
                break
                
            else:
                print("❌ 无效选择，请重试")
                
            # 给任务一些执行时间
            await asyncio.sleep(0.5)
            
    except KeyboardInterrupt:
        print("\n⏹️ 收到中断信号")
    finally:
        await coordinator.stop()
        print("🎉 系统已安全关闭")

if __name__ == "__main__":
    asyncio.run(interactive_demo())
