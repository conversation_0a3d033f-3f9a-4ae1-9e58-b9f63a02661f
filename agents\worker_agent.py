"""
工作智能体 - 执行具体任务的智能体
"""
import asyncio
import random
from typing import Dict, Any
from .base_agent import BaseAgent, Message, AgentStatus

class WorkerAgent(BaseAgent):
    """工作智能体类"""
    
    def __init__(self, agent_id: str, name: str, specialization: str = "general"):
        super().__init__(agent_id, name)
        self.specialization = specialization
        self.capabilities = [specialization, "basic_tasks"]
        self.current_task = None
        
    async def handle_message(self, message: Message):
        """处理接收到的消息"""
        print(f"📨 {self.name} 收到消息: {message.message_type} 来自 {message.sender_id}")
        
        if message.message_type == "task_assignment":
            await self._handle_task_assignment(message)
        elif message.message_type == "collaboration_request":
            await self._handle_collaboration_request(message)
        elif message.message_type == "status_query":
            await self._handle_status_query(message)
        else:
            print(f"⚠️ 未知消息类型: {message.message_type}")
            
    async def _handle_task_assignment(self, message: Message):
        """处理任务分配"""
        task = message.content.get("task")
        if not task:
            return
            
        print(f"🎯 {self.name} 接收到任务: {task.get('description', 'Unknown task')}")
        
        # 检查是否能处理该任务
        required_capability = task.get("required_capability", "basic_tasks")
        if required_capability not in self.capabilities:
            # 发送拒绝消息
            await self.send_message(
                message.sender_id,
                "task_rejection",
                {
                    "task_id": task.get("id"),
                    "reason": f"缺少必要能力: {required_capability}"
                }
            )
            return
            
        # 接受任务
        self.current_task = task
        self.status = AgentStatus.BUSY
        
        await self.send_message(
            message.sender_id,
            "task_acceptance",
            {"task_id": task.get("id")}
        )
        
        # 执行任务
        result = await self.execute_task(task)
        
        # 发送结果
        await self.send_message(
            message.sender_id,
            "task_completion",
            {
                "task_id": task.get("id"),
                "result": result
            }
        )
        
        self.current_task = None
        self.status = AgentStatus.IDLE
        
    async def _handle_collaboration_request(self, message: Message):
        """处理协作请求"""
        request = message.content
        print(f"🤝 {self.name} 收到协作请求: {request.get('description')}")
        
        # 简单的协作逻辑 - 如果空闲就接受
        if self.status == AgentStatus.IDLE:
            await self.send_message(
                message.sender_id,
                "collaboration_acceptance",
                {
                    "request_id": request.get("id"),
                    "capabilities": self.capabilities
                }
            )
        else:
            await self.send_message(
                message.sender_id,
                "collaboration_rejection",
                {
                    "request_id": request.get("id"),
                    "reason": "当前忙碌"
                }
            )
            
    async def _handle_status_query(self, message: Message):
        """处理状态查询"""
        await self.send_message(
            message.sender_id,
            "status_response",
            self.get_status()
        )
        
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务"""
        task_type = task.get("type", "unknown")
        print(f"⚙️ {self.name} 开始执行任务: {task_type}")
        
        # 模拟任务执行时间
        execution_time = random.uniform(1, 3)
        await asyncio.sleep(execution_time)
        
        # 模拟任务结果
        success_rate = 0.9  # 90% 成功率
        success = random.random() < success_rate
        
        result = {
            "success": success,
            "execution_time": execution_time,
            "agent_id": self.agent_id,
            "specialization": self.specialization
        }
        
        if success:
            result["output"] = f"任务 {task_type} 由 {self.specialization} 专家成功完成"
            print(f"✅ {self.name} 成功完成任务")
        else:
            result["error"] = "任务执行失败"
            print(f"❌ {self.name} 任务执行失败")
            
        return result
