"""
工作智能体 - 执行具体任务的智能体
"""
import asyncio
import random
from typing import Dict, Any
from .base_agent import BaseAgent, Message, AgentStatus

class WorkerAgent(BaseAgent):
    """工作智能体类"""

    def __init__(self, agent_id: str, name: str, specialization: str = "general"):
        super().__init__(agent_id, name)
        self.specialization = specialization
        self.capabilities = [specialization, "basic_tasks"]
        self.current_task = None

    async def handle_message(self, message: Message):
        """处理接收到的消息"""
        print(f"📨 {self.name} 收到消息: {message.message_type}")

        if message.message_type == "task_assignment":
            await self._handle_task_assignment(message)
        elif message.message_type == "collaboration_request":
            await self._handle_collaboration_request(message)
        elif message.message_type == "status_query":
            await self._handle_status_query(message)

    async def _handle_task_assignment(self, message: Message):
        """处理任务分配"""
        task = message.content.get("task")
        print(f"🎯 {self.name} 接收到任务: {task.get('description')}")

        self.current_task = task
        self.status = AgentStatus.BUSY

        # 执行任务
        result = await self.execute_task(task)

        # 发送结果给协调器
        await self.send_message(
            "coordinator",
            "task_completion",
            {"task_id": task.get("id"), "result": result}
        )

        self.current_task = None
        self.status = AgentStatus.IDLE

    async def _handle_collaboration_request(self, message: Message):
        """处理协作请求"""
        request = message.content
        print(f"🤝 {self.name} 收到协作请求")

        if self.status == AgentStatus.IDLE:
            await self.send_message(
                message.sender_id,
                "collaboration_acceptance",
                {"capabilities": self.capabilities}
            )

    async def _handle_status_query(self, message: Message):
        """处理状态查询"""
        await self.send_message(
            message.sender_id,
            "status_response",
            self.get_status()
        )

    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务"""
        task_type = task.get("type", "unknown")
        print(f"⚙️ {self.name} 开始执行 {task_type} 任务")

        # 模拟任务执行
        execution_time = random.uniform(1, 3)
        await asyncio.sleep(execution_time)

        success = random.random() < 0.9

        result = {
            "success": success,
            "execution_time": execution_time,
            "agent_id": self.agent_id,
            "specialization": self.specialization
        }

        if success:
            result["output"] = f"{task_type} 任务完成"
            print(f"✅ {self.name} 成功完成任务")
        else:
            result["error"] = "任务执行失败"
            print(f"❌ {self.name} 任务执行失败")

        return result
