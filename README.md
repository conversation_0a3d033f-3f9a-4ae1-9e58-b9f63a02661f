# A2A 多智能体协作研究项目

## 项目简介
这是一个用于研究智能体对智能体（Agent-to-Agent）协作的实验性项目。系统支持多个智能体之间的通信、协调和协作完成复杂任务。

## 核心特性
- 🤖 多智能体架构
- 💬 异步消息通信
- 🎯 任务分配与协调
- 🔄 协作决策机制
- 📊 性能监控

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行示例
```bash
# 启动协调器
python coordinator.py

# 在另一个终端启动智能体
python run_agents.py
```

## 项目结构
```
├── agents/          # 智能体实现
├── communication/   # 通信模块
├── examples/        # 示例场景
├── tests/          # 测试用例
└── docs/           # 文档
```

## 研究场景
1. **任务分解协作** - 多智能体协同完成复杂任务
2. **资源竞争** - 智能体间的资源分配与协商
3. **信息共享** - 分布式信息收集与整合
4. **决策共识** - 多智能体协商达成一致决策
