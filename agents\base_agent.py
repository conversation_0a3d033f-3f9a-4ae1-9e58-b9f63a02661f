"""
基础智能体类 - 所有智能体的父类
"""
import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

class AgentStatus(Enum):
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"

@dataclass
class Message:
    """智能体间通信消息"""
    id: str
    sender_id: str
    receiver_id: str
    message_type: str
    content: Dict[str, Any]
    timestamp: float
    
    def to_json(self) -> str:
        return json.dumps(self.__dict__)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        data = json.loads(json_str)
        return cls(**data)

class BaseAgent(ABC):
    """基础智能体类"""
    
    def __init__(self, agent_id: str, name: str):
        self.agent_id = agent_id
        self.name = name
        self.status = AgentStatus.IDLE
        self.capabilities: List[str] = []
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.coordinator_connection = None
        self.running = False
        
    async def start(self):
        """启动智能体"""
        self.running = True
        self.status = AgentStatus.IDLE
        print(f"🤖 智能体 {self.name} ({self.agent_id}) 已启动")
        
        # 启动消息处理循环
        asyncio.create_task(self._message_loop())
        
    async def stop(self):
        """停止智能体"""
        self.running = False
        self.status = AgentStatus.OFFLINE
        print(f"🛑 智能体 {self.name} 已停止")
        
    async def send_message(self, receiver_id: str, message_type: str, content: Dict[str, Any]):
        """发送消息给其他智能体"""
        message = Message(
            id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content,
            timestamp=asyncio.get_event_loop().time()
        )
        
        # 这里应该通过协调器转发消息
        if self.coordinator_connection:
            await self.coordinator_connection.send_message(message)
        
    async def receive_message(self, message: Message):
        """接收消息"""
        await self.message_queue.put(message)
        
    async def _message_loop(self):
        """消息处理循环"""
        while self.running:
            try:
                # 等待消息，超时1秒
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                await self.handle_message(message)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"❌ 智能体 {self.name} 处理消息时出错: {e}")
                
    @abstractmethod
    async def handle_message(self, message: Message):
        """处理接收到的消息 - 子类必须实现"""
        pass
        
    @abstractmethod
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务 - 子类必须实现"""
        pass
        
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "capabilities": self.capabilities
        }
