"""
运行多智能体系统的启动脚本
"""
import asyncio
from coordinator import TaskCoordinator

async def run_demo():
    """运行演示"""
    print("🎬 启动A2A多智能体协作演示")
    print("=" * 50)
    
    coordinator = TaskCoordinator()
    await coordinator.start()
    
    try:
        # 创建不同类型的任务来展示协作
        tasks = [
            ("数据分析", "分析销售数据趋势", "data_analysis"),
            ("文本处理", "提取关键词和情感分析", "text_processing"),
            ("计算任务", "优化算法参数", "computation"),
            ("数据分析", "用户行为模式识别", "data_analysis"),
            ("文本处理", "多语言文档翻译", "text_processing"),
        ]
        
        print(f"\n📋 创建 {len(tasks)} 个任务...")
        for task_type, description, capability in tasks:
            await coordinator.create_task(task_type, description, capability)
            await asyncio.sleep(0.5)  # 间隔创建
            
        print("\n⏳ 等待任务执行...")
        await asyncio.sleep(15)  # 等待任务完成
        
        # 显示最终状态
        print("\n" + "=" * 50)
        print("📊 最终系统状态:")
        status = coordinator.get_system_status()
        
        print(f"\n🤖 智能体状态:")
        for agent_id, agent_info in status['agents'].items():
            print(f"  • {agent_info['name']}: {agent_info['status']}")
            
        print(f"\n📈 任务统计:")
        tasks_info = status['tasks']
        print(f"  • 总任务数: {tasks_info['total']}")
        print(f"  • 待处理: {tasks_info['pending']}")
        print(f"  • 执行中: {tasks_info['assigned']}")
        print(f"  • 已完成: {tasks_info['completed']}")
        
        completion_rate = (tasks_info['completed'] / tasks_info['total']) * 100 if tasks_info['total'] > 0 else 0
        print(f"  • 完成率: {completion_rate:.1f}%")
        
        print("\n🎉 演示完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断演示")
    finally:
        await coordinator.stop()

if __name__ == "__main__":
    asyncio.run(run_demo())
