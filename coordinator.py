"""
协调器 - 管理多智能体协作的中央控制器
"""
import asyncio
import uuid
from typing import Dict, List, Any, Optional
from agents.base_agent import Message, AgentStatus
from agents.worker_agent import WorkerAgent

class TaskCoordinator:
    """任务协调器"""

    def __init__(self):
        self.agents: Dict[str, WorkerAgent] = {}
        self.tasks: Dict[str, Dict] = {}
        self.running = False
        self._task_assignment_task: Optional[asyncio.Task] = None

    async def start(self):
        """启动协调器"""
        self.running = True
        print("🎯 任务协调器已启动")

        # 创建示例智能体
        await self._create_sample_agents()

        # 启动任务分配循环并保存任务引用
        self._task_assignment_task = asyncio.create_task(self._task_assignment_loop())

    async def stop(self):
        """停止协调器"""
        self.running = False

        # 取消任务分配循环
        if self._task_assignment_task and not self._task_assignment_task.done():
            self._task_assignment_task.cancel()
            try:
                await self._task_assignment_task
            except asyncio.CancelledError:
                pass  # 预期的取消异常

        # 停止所有智能体
        for agent in self.agents.values():
            await agent.stop()
        print("🛑 协调器已停止")
        
    async def _create_sample_agents(self):
        """创建示例智能体"""
        agent_configs = [
            ("agent_1", "数据分析师", "data_analysis"),
            ("agent_2", "文本处理器", "text_processing"),
            ("agent_3", "计算专家", "computation"),
        ]
        
        for agent_id, name, specialization in agent_configs:
            agent = WorkerAgent(agent_id, name, specialization)
            agent.coordinator_connection = self
            self.agents[agent_id] = agent
            await agent.start()
            
    async def send_message(self, message: Message):
        """转发消息给目标智能体"""
        if message.receiver_id in self.agents:
            await self.agents[message.receiver_id].receive_message(message)
        elif message.receiver_id == "coordinator":
            await self._handle_coordinator_message(message)
            
    async def _handle_coordinator_message(self, message: Message):
        """处理发给协调器的消息"""
        if message.message_type == "task_completion":
            await self._handle_task_completion(message)
            
    async def _handle_task_completion(self, message: Message):
        """处理任务完成消息"""
        task_id = message.content.get("task_id")
        result = message.content.get("result")
        
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "completed"
            self.tasks[task_id]["result"] = result
            print(f"✅ 任务 {task_id} 已完成")
            
    async def create_task(self, task_type: str, description: str, required_capability: str = "basic_tasks"):
        """创建新任务"""
        task_id = str(uuid.uuid4())[:8]
        task = {
            "id": task_id,
            "type": task_type,
            "description": description,
            "required_capability": required_capability,
            "status": "pending",
            "created_at": asyncio.get_event_loop().time()
        }
        
        self.tasks[task_id] = task
        print(f"📋 创建新任务: {description} (ID: {task_id})")
        return task_id
        
    async def assign_task(self, task_id: str):
        """分配任务给合适的智能体"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        required_capability = task.get("required_capability", "basic_tasks")
        
        # 寻找有能力且空闲的智能体
        suitable_agents = [
            agent for agent in self.agents.values()
            if required_capability in agent.capabilities and agent.status == AgentStatus.IDLE
        ]
        
        if not suitable_agents:
            print(f"⚠️ 没有合适的智能体处理任务 {task_id}")
            return False
            
        # 选择第一个合适的智能体
        selected_agent = suitable_agents[0]
        
        # 发送任务分配消息
        message = Message(
            id=str(uuid.uuid4()),
            sender_id="coordinator",
            receiver_id=selected_agent.agent_id,
            message_type="task_assignment",
            content={"task": task},
            timestamp=asyncio.get_event_loop().time()
        )
        
        await self.send_message(message)
        self.tasks[task_id]["status"] = "assigned"
        self.tasks[task_id]["assigned_to"] = selected_agent.agent_id
        
        print(f"📤 任务 {task_id} 已分配给 {selected_agent.name}")
        return True
        
    async def _task_assignment_loop(self):
        """任务分配循环"""
        while self.running:
            # 检查待分配的任务
            pending_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task["status"] == "pending"
            ]
            
            for task_id in pending_tasks:
                await self.assign_task(task_id)
                
            await asyncio.sleep(2)  # 每2秒检查一次
            
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        agent_status = {
            agent_id: agent.get_status()
            for agent_id, agent in self.agents.items()
        }
        
        task_summary = {
            "total": len(self.tasks),
            "pending": len([t for t in self.tasks.values() if t["status"] == "pending"]),
            "assigned": len([t for t in self.tasks.values() if t["status"] == "assigned"]),
            "completed": len([t for t in self.tasks.values() if t["status"] == "completed"])
        }
        
        return {
            "agents": agent_status,
            "tasks": task_summary,
            "running": self.running
        }

async def main():
    """主函数"""
    coordinator = TaskCoordinator()
    await coordinator.start()
    
    try:
        # 创建一些示例任务
        await coordinator.create_task("数据分析", "分析用户行为数据", "data_analysis")
        await coordinator.create_task("文本处理", "处理客户反馈文本", "text_processing")
        await coordinator.create_task("计算任务", "执行复杂数学计算", "computation")
        
        # 运行一段时间
        print("\n🚀 系统运行中...")
        await asyncio.sleep(10)
        
        # 显示系统状态
        status = coordinator.get_system_status()
        print(f"\n📊 系统状态:")
        print(f"智能体数量: {len(status['agents'])}")
        print(f"任务统计: {status['tasks']}")
        
    except KeyboardInterrupt:
        print("\n⏹️ 收到停止信号")
    finally:
        await coordinator.stop()

if __name__ == "__main__":
    asyncio.run(main())
